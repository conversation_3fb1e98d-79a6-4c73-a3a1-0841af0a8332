package com.sbg.ug.optimus_backend.billing.orchastration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;


@Component
public class TransactionOrchestrator {
    private static final Logger logger = LoggerFactory.getLogger(TransactionOrchestrator.class);

    @Autowired
    private TaskExecutor taskExecutor;

    public TransactionBuilder newTransaction() {
        return new TransactionBuilder();
    }

    public class TransactionBuilder {
        private final List<TransactionStep<?>> steps = new ArrayList<>();
        private final TransactionContext context = new TransactionContext();
        private boolean enableCompensation = true;

        public <T> TransactionBuilder addStep(TransactionStep<T> step) {
            steps.add(step);
            return this;
        }

        public <T> TransactionBuilder addStep(String name, DistributedAction<T> action) {
            steps.add(new TransactionStep.Builder<T>()
                    .name(name)
                    .action(action)
                    .build());
            return this;
        }

        public <T> TransactionBuilder addConditionalStep(String name,
                                                         DistributedAction<T> action,
                                                         Function<TransactionContext, Boolean> condition) {
            steps.add(new TransactionStep.Builder<T>()
                    .name(name)
                    .action(action)
                    .condition(condition)
                    .build());
            return this;
        }

        public TransactionBuilder withCompensation(boolean enabled) {
            this.enableCompensation = enabled;
            return this;
        }

        public TransactionBuilder setContextValue(String key, Object value) {
            context.put(key, value);
            return this;
        }

        public CompletableFuture<TransactionResult> executeAsync() {
            return CompletableFuture.supplyAsync(this::execute, taskExecutor);
        }

        public TransactionResult execute() {
            List<TransactionStep<?>> executedSteps = new ArrayList<>();

            try {
                for (TransactionStep<?> step : steps) {
                    if (!step.getCondition().apply(context)) {
                        logger.info("Skipping step '{}' due to condition", step.getName());
                        continue;
                    }

                    Object result = executeStepWithRetry(step);
                    context.put(step.getName() + "_result", result);
                    context.addExecutedStep(step.getName());
                    executedSteps.add(step);

                    logger.info("Successfully executed step: {}", step.getName());
                }

                return TransactionResult.success(context);

            } catch (Exception e) {
                logger.error("Transaction failed at step", e);

                if (enableCompensation) {
                    compensate(executedSteps);
                }

                return TransactionResult.failure(e, context);
            }
        }

        private <T> T executeStepWithRetry(TransactionStep<T> step) throws Exception {
            RetryPolicy policy = step.getRetryPolicy();
            Exception lastException = null;

            for (int attempt = 1; attempt <= policy.maxAttempts(); attempt++) {
                try {
                    logger.debug("Executing step '{}', attempt {}/{}",
                            step.getName(), attempt, policy.maxAttempts());

                    return step.getAction().execute(context);

                } catch (Exception e) {
                    lastException = e;
                    step.getErrorHandler().accept(e);

                    if (attempt == policy.maxAttempts()) {
                        break;
                    }

                    long delay = calculateDelay(policy, attempt);
                    logger.warn("Step '{}' failed on attempt {}, retrying in {}ms",
                            step.getName(), attempt, delay, e);

                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during retry delay", ie);
                    }
                }
            }

            throw new RuntimeException("Step '" + step.getName() + "' failed after " +
                    policy.maxAttempts() + " attempts", lastException);
        }

        private void compensate(List<TransactionStep<?>> executedSteps) {
            logger.info("Starting compensation for {} executed steps", executedSteps.size());

            // Compensate in reverse order
            Collections.reverse(executedSteps);

            for (TransactionStep<?> step : executedSteps) {
                try {
                    step.getAction().compensate();
                    logger.info("Compensated step: {}", step.getName());
                } catch (Exception e) {
                    logger.error("Failed to compensate step: {}", step.getName(), e);
                }
            }
        }

        private long calculateDelay(RetryPolicy policy, int attempt) {
            long delay = (long) (policy.initialDelay() *
                    Math.pow(policy.backoffMultiplier(), attempt - 1));
            return Math.min(delay, policy.maxDelay());
        }
    }
}